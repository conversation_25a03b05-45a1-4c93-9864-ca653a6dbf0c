<template>
    <div class="box">
        <div v-if="!isPaid">
            <!-- <button v-if="readyPay" class="readyPay" @click="create">购买</button> -->
            <img src="@/assets/yinyuejie/result/buy.png" v-if="readyPay" class="readyPay" @click="create">
            <img :src="generatedQRCode" class="qrcode" v-if="generatedQRCode && !readyPay">
            <div>限时特惠： <span style="color: rgb(255 231 0)">{{this.vipAmount/100}}元</span></div>
            <div>请使用<span style="color: rgb(0 255 50)">微信</span>或<span style="color: rgb(0 46 255)">支付宝</span>扫码支付</div>
        </div>
        <div v-if="isPaid">
            <img :src="payok" class="qrcode">
            <div>支付成功</div>
            <div v-if="makeStatue == 1">准备数据</div>
            <div v-if="makeStatue == -1">数据出错</div>
            <div v-if="makeStatue == 2">正在制作中...</div>
        </div>
        <makeImg :now="selectItem" ref="makeImg" class="mkimg"></makeImg>
        <div class="cover" v-if="coverShow"></div>
        <div class="testPrice" @click="testPrice"> </div>
    </div>
</template>
<script>
import payok from '@/assets/images/payok.png'
import axios from 'axios'
import {uploadImg} from './uploadImg.js'
import QRCode from 'qrcode'
import makeImg from './result_makeImg.vue'
import { handleError } from 'vue'
import { useSinglePhotoStore } from '@/store/single'
import {imgListArr} from './gridData.js'
import { ElMessage } from 'element-plus'
export default {
    components: {
        makeImg
    },
    props: {
        results: Array,
        currentIndex: Number,
        subjectFile: File,
        gender: String,
    },
    data() {
        return {
            makeStatue: 0,
            readyPay: true,
            payok,
            amount: 59,
            qrCodeData: '',
            generatedQRCode: '',
            qrCodeOptions: {
                width: 200,
                height: 200,
                margin: 1,
                color: {
                    dark: '#000000',
                    light: '#ffffff'
                }
            },
            isPaid: false,
            styleBodyShapeMap: {
                "001": { style: "loki", body_shape: "default" },
                "002": { style: "frog", body_shape: "mini" },
                "003": { style: "long", body_shape: "mini" },
                "004": { style: "luna", body_shape: "mini" },
                "005": { style: "soldier", body_shape: "default" },
                "006": { style: "coco", body_shape: "default" },
                "007": { style: "ye", body_shape: "default" },
                "008": { style: "yao", body_shape: "mini" },
                "009": { style: "cyber", body_shape: "mini" }
            },
            stylesMap:[],
            order: {
                business_order_id: '', // 业务订单号
                digital_avatar_id: '', // 订单级数字形象ID
            },
            selectItem: {},
            orderInterval: null,
            isPayLst: [],
            coverShow: false,
            amount: 7900,
            vipAmount: 5900,
            good: {}
        }
    },
    watch: {
        currentIndex: {
            handler(newVal) {
                if (newVal>-1) {
                    this.init()
                }
            }
            // this.create()
        },
        qrCodeData: {
            handler(newVal) {
                if (newVal) {
                    this.generateQRCode()
                }
            },
            immediate: true
        }
    },
    created(){
        axios.post('/api/v1.0/device/get_goods_list').then(e=>{
            let data = e.data.data.goods_list.find(e=>e.type == 'NFCKeyChain')
            this.amount = data.price
            this.vipAmount = data.price
            this.good = data
        })
    },
    methods: {
        init(){
            this.coverShow = false
            this.readyPay = true
            this.clearOrderInterval()
            this.isPaid = false
            this.makeStatue = 0
            this.selectItem = this.results[this.currentIndex]
            this.orderInterval && clearInterval(this.orderInterval)
            this.$refs['makeImg'].init('1111')
            console.log(1)
        },
        testPrice() {
            this.amount = 2
            this.vipAmount = 2
        },
        clearOrderInterval() {
            if (this.orderInterval) {
                clearInterval(this.orderInterval)
                this.orderInterval = null
            }
        },
        generateQRCode() {
            if (!this.qrCodeData) return
            
            QRCode.toDataURL(this.qrCodeData, this.qrCodeOptions)
                .then(url => {
                    this.generatedQRCode = url
                })
                .catch(err => {
                    console.error('QR Code 生成错误:', err)
                })
        },
        create() {
            const useSingleStore = useSinglePhotoStore()
            let styleLst = useSingleStore.stylesData
            console.log(this.results)
            let item = this.results[this.currentIndex]
            let style = styleLst.find(e=>e.code_2d == item.style.slice(2))?.code
            this.coverShow = true
            axios.post('/api/v1.0/payment/create_order', {
                "business_data": [
                    {
                        "goods_id": this.good.id,
                        "goods_num": 1,
                        "goods_type": this.good.type,
                        "style": style,
                        "pic_url": item.result_oss,
                        "discount_id": null
                    }
                ],
                "payment_amount": this.vipAmount,// 5900,
                "original_amount": this.amount,// 7900,
                "discount_id": null,
                "origi_pic_url": history.state.objectKey,
                "digital_avatar_id": history.state.avatarId
            }).then(res => {
                console.log(res.data)
                this.qrCodeData = res.data.data.qrcode_url
                this.amount = res.data.data.payment_amount/100
                this.isPaid = false
                this.readyPay = false
                this.order = {
                    business_order_id: res.data.data.business_order_id,
                    digital_avatar_id: res.data.data.digital_avatar_id
                }
                this.waitPay()
            }).catch(err => {
                this.qrCodeData = 'res.data.data.qrcode_url'
                this.isPaid = false
                this.readyPay = true
                this.coverShow = false
                console.log(err)
                ElMessage({
                    message: err.msg || '发生错误',
                    type: 'error'
                })
            })
        },
        waitPay() {
            if (this.orderInterval) {
                clearInterval(this.orderInterval)
                this.orderInterval = null
            }
            let tempId = this.order.business_order_id
            this.orderInterval = setInterval(() => {
                axios.get('/api/v1.0/payment/order_status',{
                    params:{business_order_id: tempId}
                }).then(res => {
                    if (res.data.data.pay_status == 1 ){
                        if (res.data.data.business_order_id == tempId){
                            
                            // this.makeAvatar()
                            this.isPaid = true
                            this.readyPay = true
                            this.coverShow = false
                            this.makeStatue = 2
                        }
                        this.clearOrderInterval()
                        
                    }
                })
            },1000)
        },
        makeAvatar() {
            this.makeStatue = 1
            
                
            axios.post('api/v1.0/digital-avatar/init',{
                client_id: window.clientId.screen_num,
                digital_avatar_id: this.order.digital_avatar_id,
                avatar: this.selectItem.avatar_image,
                
                avatar_q_style: this.selectItem.result_oss,
                style: this.styleBodyShapeMap[this.selectItem.code].style,
                body_shape: this.styleBodyShapeMap[this.selectItem.code].body_shape,
                request_id: crypto.randomUUID().replace(/-/g, ""),
                gender: this.gender
            }).then(res => {
                let miniqrcode = res.data.data.qr_code_url
                let imgs = this.$refs['makeImg'].init(res.data.data.lucky_id, res.data.data.qr_code_url)
                imgs.then((res) => {
                    let promiseLst = []
                    res.forEach((item) => {
                        let p = uploadImg(item, this.order.digital_avatar_id)
                        promiseLst.push(p)
                    })
                    Promise.all(promiseLst).then((res) => {
                        axios.post('/api/v1.0/digital-avatar/upload_print_pic',{
                            client_id: window.clientId.screen_num,
                            request_id: crypto.randomUUID().replace(/-/g, ""),
                            digital_avatar_id: this.order.digital_avatar_id,
                            print_front_path: res[0].objectKey,
                            print_back_path: res[1].objectKey
                        }).then(e=>{
                            this.isPaid = true
                            this.readyPay = true
                            this.coverShow = false
                            this.makeStatue = 2
                        }).catch(e=>{
                            this.makeStatue = -1
                        })
                    })
                })
                
            })
        
        }
    },
    unmounted() {
        this.clearOrderInterval()
    },
    mounted() {
        console.log(this.results)
        console.log(this.currentIndex)
        console.log(this.subjectFile)
        console.log(this.gender)
        setTimeout(() => {
            this.selectItem = this.results[this.currentIndex]
        },100)
        
        // setTimeout(this.makeAvatar,1000)
        // this.create()
    },
}
</script>
<style scoped>
.testPrice {
    position: fixed;
    top: 0;
    height: 2vh;
    width: 2vh!important;
    left: 50%;
    z-index: 9999;
    transform: translateX(-50%);
}
.cover{
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1001;
}
.mkimg{
    z-index: -3
}
.readyPay{
    /* background-color: rgb(194, 73, 246); */
    width: 500rem;
    border: none;
    color: white;
    /* padding: 10rem 50rem; */
    margin-bottom: -30rem;
    /* border-radius: 15rem; */
    text-align: center;
    font-size: 50rem;
}
.box {
    position: absolute;
    /* bottom: -20rem; */
    color: white;
    font-size: 35rem;
    /* font-weight: bold; */
    text-align: center;
    width: 100%;
}
.box >div{
    width: 100%;
}
.qrcode {
    width: 15%;
    /* height: 10%; */
}
</style>