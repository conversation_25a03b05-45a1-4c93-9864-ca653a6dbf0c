!macro preInit
  ; 根据产品名称设置不同的安装目录
  SetRegView 64
  ${If} "${PRODUCT_NAME}" == "popofifi-test"
    ; 测试版安装到 Program Files\popofifi-test
    WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
    WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
    StrCpy $INSTDIR "$PROGRAMFILES\popofifi-test"
  ${Else}
    ; 生产版安装到 Program Files\popofifi
    WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi"
    WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi"
    StrCpy $INSTDIR "$PROGRAMFILES\popofifi"
  ${EndIf}
  SetRegView 32
  ${If} "${PRODUCT_NAME}" == "popofifi-test"
    WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
    WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi-test"
  ${Else}
    WriteRegExpandStr HKLM "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi"
    WriteRegExpandStr HKCU "${INSTALL_REGISTRY_KEY}" InstallLocation "$PROGRAMFILES\popofifi"
  ${EndIf}
!macroend

!macro customInit
  ; 在初始化时强制设置安装目录
  ${If} "${PRODUCT_NAME}" == "popofifi-test"
    StrCpy $INSTDIR "$PROGRAMFILES\popofifi-test"
  ${Else}
    StrCpy $INSTDIR "$PROGRAMFILES\popofifi"
  ${EndIf}
!macroend
